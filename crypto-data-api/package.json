{"name": "crypto-data-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "engines": {"node": ">= v18.13.0"}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --testTimeout=60000", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --testTimeout=60000 --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "10.1.2", "@nestjs/core": "10.1.2", "@nestjs/platform-express": "^10.1.2", "@nestjs/schedule": "^3.0.1", "cache-manager": "5.2.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "crypto-data": "file:crypto-data-1.1.5.tgz", "decimal.js": "^10.4.3", "lru-cache": "^10.0.0", "numeral": "^2.0.6", "reflect-metadata": "^0.2.2", "rimraf": "^5.0.1", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cache-manager": "^2.1.0", "@nestjs/cli": "^10.1.10", "@nestjs/schematics": "^10.0.1", "@nestjs/testing": "^10.1.2", "@types/express": "^4.17.17", "@types/jest": "29.5.3", "@types/node": "^20.4.5", "@types/numeral": "^2.0.2", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "eslint": "^8.46.0", "eslint-config-prettier": "^8.9.0", "eslint-plugin-prettier": "^5.0.0", "jest": "29.6.2", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "29.1.1", "ts-loader": "^9.4.4", "ts-node": "^10.9.1", "tsconfig-paths": "4.2.0", "typescript": "^5.1.6"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}