import { Injectable } from '@nestjs/common';
import {
    findBtc2YearMovingAverage as _findBtc2YearMovingAverage,
    findRain<PERSON><PERSON><PERSON> as _findRainbow<PERSON><PERSON>,
} from 'crypto-data';
import { Btc2YearMovingAverage } from 'crypto-data/lib/src/process/crypto-images';

@Injectable()
export class ImageService {
    async findRainbow<PERSON>hart(): Promise<{ originUrl: string; base64Img: string }> {
        const { originUrl, base64Img } = await _findRainbow<PERSON>hart();
        return { originUrl, base64Img };
    }

    async findBtc2YearMovingAverage(): Promise<{ originUrl: string; base64Img: string }> {
        const { originUrl, base64Img }: Btc2YearMovingAverage = await _findBtc2YearMovingAverage();
        return { originUrl, base64Img };
    }
}
