{"name": "crypto-data", "version": "1.1.5", "description": "Module for accessing crypto data", "engines": {"node": ">= v18.13.0"}, "main": "./lib/src/index.js", "types": "./lib/src/index.d.ts", "devDependencies": {"@types/chai-as-promised": "^7.1.5", "@types/cors": "^2.8.13", "@types/debug": "^4.1.8", "@types/inversify": "^2.0.33", "@types/jsdom": "^21.1.1", "@types/mocha": "^10.0.1", "@types/node": "~20.4.8", "@types/node-fetch": "^2.6.4", "@typescript-eslint/eslint-plugin": "~6.2.1", "@typescript-eslint/parser": "~6.2.1", "chai": "^4.3.7", "chai-as-promised": "^7.1.1", "eslint": "~8.46.0", "eslint-config-prettier": "~9.0.0", "eslint-plugin-chai-expect": "^3.0.0", "eslint-plugin-jest": "~27.2.3", "eslint-plugin-mocha": "^10.1.0", "mocha": "^10.2.0", "nodemon": "^3.0.1", "nyc": "^15.1.0", "prettier": "~3.0.1", "rimraf": "~5.0.1", "source-map-support": "^0.5.21", "ts-jest": "~29.1.1", "ts-mocha": "^10.0.0", "ts-node": "^10.9.1", "tsutils": "~3.21.0", "typescript": "5.1.6"}, "scripts": {"build": "rm -rf lib & tsc -p tsconfig.release.json --declaration", "lint": "eslint --fix .. --ext .ts,.tsx", "test": "mocha --timeout 60000 --require ts-node/register --extensions ts src 'tests/**/*.{ts,tsx}'", "package": "npm pack"}, "files": ["lib/**/*"], "author": "<PERSON><PERSON><PERSON>", "license": "GNU GPLv3", "dependencies": {"chai-http": "^4.4.0", "cors": "^2.8.5", "debug": "^4.3.4", "inversify": "6.0.1", "jsdom": "^22.1.0", "node-ts-cache": "^4.4.0", "node-ts-cache-storage-memory": "^4.4.0", "puppeteer": "^21.4.1", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "reflect-metadata": "^0.1.13", "tslib": "~2.6.1", "tslog": "^4.8.5", "unique-names-generator": "^4.7.1", "uuid": "^9.0.0", "validator": "^13.11.0"}, "volta": {"node": "14.15.0"}}