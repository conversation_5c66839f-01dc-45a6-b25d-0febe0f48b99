networks:
  docker-network:
    driver: bridge
services:
  bot-database:
    image: mongo:4.0.3
    restart: always
    networks:
      - docker-network
    ports:
      - "27017:27017"
    volumes:
      - ./client/data/db:/data/db
    environment:
      MONGO_INITDB_ROOT_USERNAME: $DB_USER
      MONGO_INITDB_ROOT_PASSWORD: $DB_PASSWORD
  crypto-data-api:
    networks:
      - docker-network
    build:
      dockerfile: crypto-data-api.dev.Dockerfile
      context: ./docker
    ports:
      - "3001:3001"
    volumes:
      - ./crypto-data/src:/crypto-data/src
      - ./crypto-data/package.json:/crypto-data/package.json
      - ./crypto-data/tsconfig.json:/crypto-data/tsconfig.json
      - ./crypto-data/tsconfig.release.json:/crypto-data/tsconfig.release.json
      - ./crypto-data/install-puppeteer-extra.sh:/crypto-data-api/install-puppeteer-extra.sh
      - ./crypto-data-api/nest-cli.json:/crypto-data-api/nest-cli.json
      - ./crypto-data-api/tsconfig.build.json:/crypto-data-api/tsconfig.build.json
      - ./crypto-data-api/tsconfig.json:/crypto-data-api/tsconfig.json
      - ./crypto-data-api/package.json:/crypto-data-api/package.json
      - ./crypto-data-api/src:/crypto-data-api/src
    command: >
      sh -c "cd crypto-data && npm install && npm run build && npm pack && cp crypto-data-1.1.5.tgz ../crypto-data-api/crypto-data-1.1.5.tgz && cd .. && cd /crypto-data-api && npm install && npm run build && node dist/main.js"
  telegram-client:
    networks:
      - docker-network
    build:
      dockerfile: telegram-client.dev.Dockerfile
      context: ./docker
    ports:
      - "2999:2999"
    volumes:
      - ./client/src:/telegram-client
      - ./client/requirements.txt:/telegram-client/requirements.txt
    command: >
      sh -c "cd /telegram-client && pip3 install -r requirements.txt && python3 run_telegram_bot.py"
    environment:
      - TELEGRAM_BOT_TOKEN=$TELEGRAM_BOT_TOKEN
      - CRYPTO_DATA_API_SERVER_HOST=http://crypto-data-api
      - CRYPTO_DATA_API_SERVER_PORT=3001
      - DB_HOST=bot-database
      - DB_PORT=27017
      - DB_USER=$DB_USER
      - DB_PASSWORD=$DB_PASSWORD
    depends_on:
      - crypto-data-api
